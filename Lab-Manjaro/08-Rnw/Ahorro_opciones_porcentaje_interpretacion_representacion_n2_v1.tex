\documentclass[10pt,a4paper]{article}

%% paquetes básicos
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{tikz,xcolor}
\usepackage{enumitem}
\usepackage{colortbl}

%% bibliotecas TikZ según necesidad
\usetikzlibrary{automata,positioning,calc,arrows}

%% entornos para exams
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% comandos para metadatos exams
\newcommand{\exname}[1]{\def\@exname{#1}}
\newcommand{\extype}[1]{\def\@extype{#1}}
\newcommand{\exsolution}[1]{\def\@exsolution{#1}}
\newcommand{\exshuffle}[1]{\def\@exshuffle{#1}}
\newcommand{\exsection}[1]{\def\@exsection{#1}}

%% configuración párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\input{Ahorro_opciones_porcentaje_interpretacion_representacion_n2_v1-concordance}

\begin{enumerate}


\begin{question}

Sofia quiere ahorrar \$100.000 cada mes durante 5 meses. Como ayuda para su inversi<U+00F3>n, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.

\textbf{Opci<U+00F3>n 1.} Al finalizar cada mes, su mam<U+00E1> le regala un porcentaje del dinero que tenga acumulado en su inversi<U+00F3>n.

\begin{tabular}{|c|c|c|}
\hline
\textbf{Ahorro Acumulado} & \textbf{Mes} & \textbf{Porcentaje regalado} \\
& & \textbf{por mam<U+00E1>} \\
\hline
\$100.000 & 1 & 8\% \\
\hline
\$200.000 & 2 & 8\% \\
\hline
\$300.000 & 3 & 8\% \\
\hline
\end{tabular}
\textbf{Opci<U+00F3>n 2.} Al finalizar cada mes, su pap<U+00E1> le regala un porcentaje del dinero que tenga acumulado en su inversi<U+00F3>n.

\begin{tabular}{|c|c|c|}
\hline
\textbf{Ahorro Acumulado} & \textbf{Mes} & \textbf{Porcentaje regalado} \\
& & \textbf{por pap<U+00E1>} \\
\hline
\$100.000 & 1 & 1\% \\
\hline
\$200.000 & 2 & 3\% \\
\hline
\$300.000 & 3 & 18\% \\
\hline
\end{tabular}
Sofia decide elegir la opci<U+00F3>n en que le regalen la mayor cantidad de dinero. <U+00BF>Deber<U+00ED>a elegir la opci<U+00F3>n de mam<U+00E1>?

\begin{answerlist}
  \item No, porque la ayuda total de su pap<U+00E1> es de $164.000 mientras que la de su mam<U+00E1> es de $48.000.
  \item S<U+00ED>, porque la mam<U+00E1> da un porcentaje constante del 8% que es m<U+00E1>s confiable que los porcentajes variables del pap<U+00E1>.
  \item S<U+00ED>, porque el porcentaje m<U+00E1>ximo del pap<U+00E1> es 18% y el de la mam<U+00E1> es solo 8%.
  \item S<U+00ED>, porque con la ayuda del pap<U+00E1> recibe en promedio el 9% y con la ayuda de la mam<U+00E1> recibe el 8% del total ahorrado.
\end{answerlist}
\end{question}

\begin{solution}

Para resolver este problema, debemos calcular la ayuda total que recibir<U+00ED>a Sofia con cada opci<U+00F3>n.

\textbf{Opci<U+00F3>n 1 (mam<U+00E1>):}

Mes 1: \$1e+05 $\times$ 8\% = \$8.000

Mes 2: \$2e+05 $\times$ 8\% = \$16.000

Mes 3: \$3e+05 $\times$ 8\% = \$24.000

Total mam<U+00E1>: \$48.000

\textbf{Opci<U+00F3>n 2 (pap<U+00E1>):}

Mes 1: \$1e+05 $\times$ 1\% = \$1.000

Mes 2: \$2e+05 $\times$ 3\% = \$6.000

Mes 3: \$3e+05 $\times$ 18\% = \$54.000

Total pap<U+00E1>: \$164.000

Por lo tanto, la respuesta correcta es la opci<U+00F3>n que indica que el pap<U+00E1> da m<U+00E1>s ayuda.

\textbf{An<U+00E1>lisis de errores comunes:}

\textbf{Error 1}: Confundir porcentaje promedio con c<U+00E1>lculo real sobre montos acumulados

\textbf{Error 2}: Considerar solo el <U+00FA>ltimo mes en lugar del total acumulado

\textbf{Error 3}: Sumar porcentajes directamente sin considerar las bases de c<U+00E1>lculo

\textbf{Error 4}: Invertir la conclusi<U+00F3>n por lectura incorrecta de los totales

\begin{answerlist}
  \item Verdadero. Esta opci<U+00F3>n calcula correctamente los totales de ayuda para ambas opciones y compara adecuadamente cu<U+00E1>l es mayor.
  \item Falso. Esta opci<U+00F3>n confunde la magnitud de los porcentajes con el resultado final del c<U+00E1>lculo.
  \item Falso. Esta opci<U+00F3>n contiene un error en el razonamiento o c<U+00E1>lculo matem<U+00E1>tico.
  \item Falso. Esta opci<U+00F3>n confunde el c<U+00E1>lculo de porcentajes promedio con el c<U+00E1>lculo real sobre montos acumulados.
\end{answerlist}
\end{solution}

\end{enumerate}

\end{document}

%% META-INFORMATION
\exname{Ahorro con porcentajes - Comparacion de opciones}
\extype{schoice}
\exsolution{1000}
\exshuffle{TRUE}
\exsection{Pensamiento Aleatorio}
